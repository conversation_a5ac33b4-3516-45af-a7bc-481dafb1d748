import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/souvenir/category_enum.dart';

/// 分类筛选标签组件
class CategoryTabs extends ConsumerWidget {
  const CategoryTabs({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedCategory = ref.watch(selectedCategoryProvider);

    return Container(
      height: 56,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      decoration: const BoxDecoration(color: Colors.white),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: ItemCategory.values.map((category) {
          final isSelected = selectedCategory == category;

          return GestureDetector(
            onTap: () {
              // 更新选中的分类
              ref.read(selectedCategoryProvider.notifier).state = category;
            },
            child: Container(
              margin: const EdgeInsets.only(right: 32),
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 标签文字
                  Text(
                    category.label,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                      color: isSelected
                          ? const Color(0xFF333333)
                          : const Color(0xFF999999),
                      height: 1.2,
                    ),
                  ),
                  const SizedBox(height: 6),
                  // 下划线指示器
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    height: 3,
                    width: _getIndicatorWidth(category.label),
                    decoration: BoxDecoration(
                      color: isSelected ? const Color(0xFF333333) : Colors.transparent,
                      borderRadius: BorderRadius.circular(1.5),
                    ),
                  ),
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  /// 根据文字内容计算指示器宽度
  double _getIndicatorWidth(String text) {
    switch (text) {
      case '全部':
        return 32;
      case '明信片':
        return 48;
      case '车票':
        return 32;
      default:
        return text.length * 16.0;
    }
  }
}

/// 当前选中的分类Provider
final selectedCategoryProvider = StateProvider<ItemCategory>((ref) {
  return ItemCategory.all;
});
