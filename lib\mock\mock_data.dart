import '../models/souvenir/souvenir_item.dart';
import '../models/pagination_model.dart';
import '../models/api_response.dart';
import '../models/user_model.dart';

/// Mock数据生成器
/// 
/// 用于测试页面功能，生成各种类型的模拟数据
class MockData {
  /// 生成模拟奖励数据
  static List<SouvenirItem> generateSouvenirs({
    int count = 20,
    String? category,
  }) {
    final List<SouvenirItem> items = [];
    
    for (int i = 1; i <= count; i++) {
      final itemCategory = category ?? _getRandomCategory();
      items.add(SouvenirItem(
        souvenirId: i,
        title: _generateTitle(itemCategory, i),
        imgUrl: _generateImageUrl(itemCategory, i),
        category: itemCategory,
        createdAt: DateTime.now().subtract(Duration(days: i)).toIso8601String(),
      ));
    }
    
    return items;
  }

  /// 生成分页响应数据
  static PaginatedResponse<SouvenirItem> generatePaginatedSouvenirs({
    int page = 1,
    int limit = 20,
    String? category,
    int? totalItems,
  }) {
    final total = totalItems ?? 100;
    final totalPages = (total / limit).ceil();
    final hasNext = page < totalPages;
    
    final items = generateSouvenirs(
      count: limit,
      category: category,
    );
    
    // 为每页生成不同的ID
    final adjustedItems = items.map((item) {
      final newId = ((page - 1) * limit) + item.souvenirId;
      return SouvenirItem(
        souvenirId: newId,
        title: item.title.replaceAll('${item.souvenirId}', '$newId'),
        imgUrl: item.imgUrl,
        category: item.category,
        createdAt: item.createdAt,
      );
    }).toList();
    
    final pagination = PaginationModel(
      currentPage: page,
      totalPages: totalPages,
      totalItems: total,
      hasNextPage: hasNext,
    );
    
    return PaginatedResponse(
      items: adjustedItems,
      pagination: pagination,
    );
  }

  /// 生成API响应格式的奖励数据
  static ApiResponse<PaginatedResponse<SouvenirItem>> generateSouvenirApiResponse({
    int page = 1,
    int limit = 20,
    String? category,
    bool success = true,
  }) {
    if (!success) {
      return const ApiResponse(
        code: 500,
        message: '服务器内部错误',
        data: null,
      );
    }
    
    final data = generatePaginatedSouvenirs(
      page: page,
      limit: limit,
      category: category,
    );
    
    return ApiResponse(
      code: 200,
      message: '获取成功',
      data: data,
    );
  }

  /// 生成模拟用户数据
  static UserModel generateUser({int id = 1}) {
    return UserModel(
      id: id,
      createdAt: DateTime.now().subtract(const Duration(days: 30)).toIso8601String(),
      updatedAt: DateTime.now().toIso8601String(),
      uid: 'user_$id',
      nickname: '测试用户$id',
      avatar: 'https://picsum.photos/100/100?random=$id',
    );
  }

  /// 生成认证响应数据
  static ApiResponse<AuthResponse> generateAuthResponse({
    bool success = true,
    int userId = 1,
  }) {
    if (!success) {
      return const ApiResponse(
        code: 401,
        message: '用户名或密码错误',
        data: null,
      );
    }
    
    final user = generateUser(id: userId);
    final authResponse = AuthResponse(
      token: 'mock_token_${DateTime.now().millisecondsSinceEpoch}',
      user: user,
    );
    
    return ApiResponse(
      code: 200,
      message: '登录成功',
      data: authResponse,
    );
  }

  // 私有辅助方法
  static String _getRandomCategory() {
    final categories = ['postcard', 'ticket'];
    return categories[DateTime.now().millisecond % categories.length];
  }

  static String _generateTitle(String category, int index) {
    switch (category) {
      case 'postcard':
        final postcardTitles = [
          '北京天安门明信片',
          '上海外滩夜景明信片',
          '杭州西湖明信片',
          '成都熊猫明信片',
          '桂林山水明信片',
          '厦门鼓浪屿明信片',
          '青岛海滨明信片',
          '西安兵马俑明信片',
        ];
        return postcardTitles[index % postcardTitles.length] + ' #$index';
      
      case 'ticket':
        final ticketTitles = [
          '北京地铁1号线车票',
          '上海磁悬浮列车票',
          '广州BRT快速公交票',
          '深圳地铁纪念票',
          '杭州公交电子票',
          '成都地铁熊猫票',
          '武汉轻轨纪念票',
          '南京地铁文化票',
        ];
        return ticketTitles[index % ticketTitles.length] + ' #$index';
      
      default:
        return '收藏品 #$index';
    }
  }

  static String _generateImageUrl(String category, int index) {
    // 使用不同的图片服务生成模拟图片
    final imageServices = [
      'https://picsum.photos/300/400?random=',
      'https://source.unsplash.com/300x400/?',
      'https://loremflickr.com/300/400/',
    ];
    
    switch (category) {
      case 'postcard':
        return '${imageServices[0]}postcard$index';
      case 'ticket':
        return '${imageServices[0]}ticket$index';
      default:
        return '${imageServices[0]}souvenir$index';
    }
  }
}

/// Mock数据预设
class MockDataPresets {
  /// 空数据响应
  static ApiResponse<PaginatedResponse<SouvenirItem>> get emptyResponse {
    return ApiResponse(
      code: 200,
      message: '获取成功',
      data: PaginatedResponse(
        items: const [],
        pagination: const PaginationModel(
          currentPage: 1,
          totalPages: 0,
          totalItems: 0,
          hasNextPage: false,
        ),
      ),
    );
  }

  /// 错误响应
  static ApiResponse<PaginatedResponse<SouvenirItem>> get errorResponse {
    return const ApiResponse(
      code: 500,
      message: '网络连接失败，请检查网络设置',
      data: null,
    );
  }

  /// 认证失败响应
  static ApiResponse<PaginatedResponse<SouvenirItem>> get authErrorResponse {
    return const ApiResponse(
      code: 401,
      message: '认证失败，请重新登录',
      data: null,
    );
  }

  /// 小数据集（用于快速测试）
  static ApiResponse<PaginatedResponse<SouvenirItem>> get smallDataSet {
    return MockData.generateSouvenirApiResponse(
      page: 1,
      limit: 6,
    );
  }

  /// 大数据集（用于测试分页）
  static ApiResponse<PaginatedResponse<SouvenirItem>> get largeDataSet {
    return MockData.generateSouvenirApiResponse(
      page: 1,
      limit: 20,
    );
  }

  /// 明信片分类数据
  static ApiResponse<PaginatedResponse<SouvenirItem>> get postcardData {
    return MockData.generateSouvenirApiResponse(
      page: 1,
      limit: 15,
      category: 'postcard',
    );
  }

  /// 车票分类数据
  static ApiResponse<PaginatedResponse<SouvenirItem>> get ticketData {
    return MockData.generateSouvenirApiResponse(
      page: 1,
      limit: 15,
      category: 'ticket',
    );
  }
}
