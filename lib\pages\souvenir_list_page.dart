import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../widgets/category_tabs.dart';
import '../widgets/souvenir_grid_list.dart';

/// 奖励列表页面
///
/// 整合分类筛选和奖励列表组件
/// 基于demo设计，展示小鸡旅行的奖励收藏页面
class SouvenirListPage extends ConsumerWidget {
  const SouvenirListPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('收藏'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        bottom: const PreferredSize(
          preferredSize: Size.fromHeight(1),
          child: Divider(height: 1, thickness: 1),
        ),
      ),
      backgroundColor: Colors.white,
      body: const Column(
        children: [
          // 分类筛选标签
          CategoryTabs(),
          // 三列网格列表
          Expanded(child: SouvenirGridList()),
        ],
      ),
    );
  }
}
