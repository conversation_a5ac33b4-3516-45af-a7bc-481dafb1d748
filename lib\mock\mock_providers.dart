import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/souvenir_provider.dart';
import '../models/souvenir/souvenir_item.dart';
import '../models/api_response.dart';
import 'mock_souvenir_service.dart';

/// Mock奖励服务Provider
final mockSouvenirServiceProvider = Provider<MockSouvenirService>((ref) {
  return MockSouvenirService();
});

/// Mock奖励列表Notifier
class MockSouvenirListNotifier extends StateNotifier<SouvenirListState> {
  final MockSouvenirService _mockService;
  final SouvenirQuery _initialQuery;

  MockSouvenirListNotifier(this._mockService, this._initialQuery) 
      : super(const SouvenirListState()) {
    loadSouvenirs();
  }

  /// 加载奖励列表
  Future<void> loadSouvenirs({bool refresh = false}) async {
    if (state.isLoading) return;

    final query = refresh ? _initialQuery : _initialQuery.copyWith(page: state.pagination?.currentPage ?? 1);
    
    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _mockService.getSouvenirs(
        category: query.category,
        page: query.page,
        limit: query.limit,
      );

      if (response.isSuccess && response.data != null) {
        final data = response.data!;
        final newItems = refresh ? data.items : [...state.items, ...data.items];
        
        state = state.copyWith(
          items: newItems,
          pagination: data.pagination,
          isLoading: false,
          hasMore: data.pagination.hasNextPage,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.message,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 加载更多奖励
  Future<void> loadMore() async {
    if (!state.hasMore || state.isLoading) return;

    final nextPage = (state.pagination?.currentPage ?? 0) + 1;
    final query = _initialQuery.copyWith(page: nextPage);

    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _mockService.getSouvenirs(
        category: query.category,
        page: query.page,
        limit: query.limit,
      );

      if (response.isSuccess && response.data != null) {
        final data = response.data!;
        final newItems = [...state.items, ...data.items];
        
        state = state.copyWith(
          items: newItems,
          pagination: data.pagination,
          isLoading: false,
          hasMore: data.pagination.hasNextPage,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response.message,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 刷新列表
  Future<void> refresh() async {
    await loadSouvenirs(refresh: true);
  }

  /// 清除错误
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// Mock奖励列表Provider
final mockSouvenirListProvider = StateNotifierProvider.family<MockSouvenirListNotifier, SouvenirListState, SouvenirQuery>(
  (ref, query) {
    final mockService = ref.watch(mockSouvenirServiceProvider);
    return MockSouvenirListNotifier(mockService, query);
  },
);

/// Mock默认奖励列表Provider
final mockDefaultSouvenirListProvider = mockSouvenirListProvider(const SouvenirQuery());

/// Provider覆盖配置
/// 
/// 用于在测试时替换真实的Provider
List<Override> getMockProviderOverrides() {
  return [
    // 用Mock服务替换真实服务
    souvenirListProvider.overrideWith((ref, query) {
      final mockService = MockSouvenirService();
      return MockSouvenirListNotifier(mockService, query);
    }),
  ];
}
