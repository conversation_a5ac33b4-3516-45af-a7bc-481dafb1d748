# Mock数据使用说明

本目录包含用于测试的Mock数据和服务，可以在不依赖真实API的情况下测试列表页面的各种功能。

## 文件结构

```
lib/mock/
├── mock_data.dart              # Mock数据生成器
├── mock_souvenir_service.dart  # Mock服务类
├── mock_providers.dart         # Mock Provider配置
└── README.md                   # 本文档
```

## 核心功能

### 1. Mock数据生成 (`mock_data.dart`)

提供各种类型的模拟数据：

```dart
// 生成奖励列表
final souvenirs = MockData.generateSouvenirs(count: 20, category: 'postcard');

// 生成分页响应
final response = MockData.generatePaginatedSouvenirs(page: 1, limit: 20);

// 生成API响应格式
final apiResponse = MockData.generateSouvenirApiResponse(page: 1, limit: 20);
```

### 2. Mock服务 (`mock_souvenir_service.dart`)

模拟真实的API服务：

```dart
final mockService = MockSouvenirService();

// 查询奖励列表（带网络延迟）
final response = await mockService.getSouvenirs(
  category: 'postcard',
  page: 1,
  limit: 20,
);
```

### 3. 测试场景

支持多种测试场景：

- **正常数据** (`MockScenario.normal`): 完整的模拟数据
- **空数据** (`MockScenario.empty`): 空列表状态
- **错误状态** (`MockScenario.error`): 网络错误
- **慢网络** (`MockScenario.slow`): 长延迟
- **不稳定网络** (`MockScenario.unstable`): 随机失败

## 使用方法

### 1. 在应用中使用Mock数据

```dart
void main() {
  runApp(
    ProviderScope(
      // 使用Mock Provider替换真实Provider
      overrides: getMockProviderOverrides(),
      child: MyApp(),
    ),
  );
}
```

### 2. 切换测试场景

```dart
// 设置为错误状态
MockScenarioManager.setScenario(MockScenario.error);

// 设置为空数据
MockScenarioManager.setScenario(MockScenario.empty);

// 重置为正常状态
MockScenarioManager.reset();
```

### 3. 自定义错误率

```dart
// 设置30%的请求失败率
MockSouvenirService.setErrorRate(0.3);

// 设置总数据量
MockSouvenirService.setTotalItems(50);
```

## 预设数据

### 数据类型

- **明信片类别**: 北京天安门、上海外滩、杭州西湖等8种明信片
- **车票类别**: 北京地铁、上海磁悬浮、广州BRT等8种车票
- **图片**: 使用Picsum等服务生成随机图片

### 预设响应

```dart
// 空数据响应
final empty = MockDataPresets.emptyResponse;

// 错误响应
final error = MockDataPresets.errorResponse;

// 小数据集（6个项目）
final small = MockDataPresets.smallDataSet;

// 大数据集（20个项目）
final large = MockDataPresets.largeDataSet;

// 明信片数据
final postcards = MockDataPresets.postcardData;

// 车票数据
final tickets = MockDataPresets.ticketData;
```

## 测试页面

### 1. 基础演示 (`souvenir_list_demo.dart`)

包含认证和列表功能的完整演示：

```dart
void main() {
  runApp(const SouvenirListDemoApp());
}
```

### 2. Mock数据测试 (`mock_data_test_page.dart`)

专门用于测试各种Mock场景：

```dart
void main() {
  runApp(const MockDataTestApp());
}
```

特性：
- 实时切换测试场景
- 场景状态指示器
- 详细的场景说明
- 一键重置功能

## 测试建议

### 1. 功能测试

- **正常流程**: 使用`MockScenario.normal`测试正常的数据加载和分页
- **边界情况**: 使用`MockScenario.empty`测试空数据状态
- **错误处理**: 使用`MockScenario.error`测试网络错误处理
- **性能测试**: 使用`MockScenario.slow`测试加载状态显示

### 2. UI测试

- **分类切换**: 测试不同分类的数据展示
- **下拉刷新**: 测试刷新功能
- **上拉加载**: 测试分页加载
- **错误重试**: 测试错误状态的重试功能

### 3. 状态管理测试

- **加载状态**: 验证loading指示器
- **错误状态**: 验证错误信息显示
- **空状态**: 验证空数据提示
- **数据更新**: 验证状态正确更新

## 注意事项

1. **网络延迟**: Mock服务默认有800ms延迟，模拟真实网络环境
2. **图片加载**: 使用外部图片服务，需要网络连接
3. **数据一致性**: 每次生成的数据可能不同，适合测试各种情况
4. **内存使用**: 大量数据生成时注意内存占用

## 扩展Mock数据

### 添加新的数据类型

```dart
// 在MockData类中添加新方法
static List<SouvenirItem> generateCustomSouvenirs() {
  // 自定义数据生成逻辑
}
```

### 添加新的测试场景

```dart
// 在MockScenario枚举中添加新场景
enum MockScenario {
  // 现有场景...
  customScenario,  // 新场景
}

// 在MockScenarioManager中处理新场景
case MockScenario.customScenario:
  // 自定义场景逻辑
  break;
```

这套Mock数据系统可以帮助您全面测试列表页面的各种功能和状态，确保应用在各种网络环境下都能正常工作。
