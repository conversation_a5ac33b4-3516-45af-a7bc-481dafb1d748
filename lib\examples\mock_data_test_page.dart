import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../widgets/category_tabs.dart';
import '../widgets/souvenir_grid_list.dart';
import '../mock/mock_providers.dart';
import '../mock/mock_souvenir_service.dart';

/// Mock数据测试页面
/// 
/// 专门用于测试各种Mock数据场景的页面
/// 可以快速切换不同的数据状态进行测试
class MockDataTestPage extends ConsumerStatefulWidget {
  const MockDataTestPage({super.key});

  @override
  ConsumerState<MockDataTestPage> createState() => _MockDataTestPageState();
}

class _MockDataTestPageState extends ConsumerState<MockDataTestPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Mock数据测试'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        actions: [
          // 场景切换按钮
          PopupMenuButton<MockScenario>(
            icon: const Icon(Icons.science),
            onSelected: (scenario) {
              MockScenarioManager.setScenario(scenario);
              setState(() {});
              
              // 显示提示
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('已切换到: ${_getScenarioLabel(scenario)}'),
                  duration: const Duration(seconds: 2),
                ),
              );
            },
            itemBuilder: (context) => MockScenario.values.map((scenario) {
              return PopupMenuItem(
                value: scenario,
                child: Row(
                  children: [
                    Icon(
                      _getScenarioIcon(scenario),
                      size: 20,
                      color: MockScenarioManager.currentScenario == scenario 
                          ? Theme.of(context).primaryColor 
                          : Colors.grey,
                    ),
                    const SizedBox(width: 8),
                    Text(_getScenarioLabel(scenario)),
                    if (MockScenarioManager.currentScenario == scenario)
                      const Spacer(),
                    if (MockScenarioManager.currentScenario == scenario)
                      Icon(
                        Icons.check,
                        size: 16,
                        color: Theme.of(context).primaryColor,
                      ),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
      body: Column(
        children: [
          // 当前场景指示器
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            color: _getScenarioColor(MockScenarioManager.currentScenario),
            child: Row(
              children: [
                Icon(
                  _getScenarioIcon(MockScenarioManager.currentScenario),
                  color: Colors.white,
                ),
                const SizedBox(width: 8),
                Text(
                  '当前场景: ${_getScenarioLabel(MockScenarioManager.currentScenario)}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () => _showScenarioInfo(context),
                  child: const Text(
                    '说明',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ],
            ),
          ),
          
          // 分类筛选标签
          const CategoryTabs(),
          
          // 分割线
          const Divider(height: 1, thickness: 1),
          
          // 奖励列表
          const Expanded(
            child: SouvenirGridList(),
          ),
        ],
      ),
      
      // 浮动操作按钮 - 快速重置
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          MockScenarioManager.reset();
          setState(() {});
          
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('已重置为正常场景'),
              duration: Duration(seconds: 2),
            ),
          );
        },
        child: const Icon(Icons.refresh),
      ),
    );
  }

  /// 获取场景标签
  String _getScenarioLabel(MockScenario scenario) {
    switch (scenario) {
      case MockScenario.normal:
        return '正常数据';
      case MockScenario.empty:
        return '空数据';
      case MockScenario.error:
        return '错误状态';
      case MockScenario.slow:
        return '慢网络';
      case MockScenario.unstable:
        return '不稳定网络';
    }
  }

  /// 获取场景图标
  IconData _getScenarioIcon(MockScenario scenario) {
    switch (scenario) {
      case MockScenario.normal:
        return Icons.check_circle;
      case MockScenario.empty:
        return Icons.inbox;
      case MockScenario.error:
        return Icons.error;
      case MockScenario.slow:
        return Icons.hourglass_empty;
      case MockScenario.unstable:
        return Icons.signal_wifi_bad;
    }
  }

  /// 获取场景颜色
  Color _getScenarioColor(MockScenario scenario) {
    switch (scenario) {
      case MockScenario.normal:
        return Colors.green;
      case MockScenario.empty:
        return Colors.grey;
      case MockScenario.error:
        return Colors.red;
      case MockScenario.slow:
        return Colors.orange;
      case MockScenario.unstable:
        return Colors.amber;
    }
  }

  /// 显示场景说明
  void _showScenarioInfo(BuildContext context) {
    final scenario = MockScenarioManager.currentScenario;
    String description;
    
    switch (scenario) {
      case MockScenario.normal:
        description = '正常的网络状态，返回完整的模拟数据，支持分页加载。';
        break;
      case MockScenario.empty:
        description = '模拟空数据状态，API返回空列表，用于测试空状态UI。';
        break;
      case MockScenario.error:
        description = '模拟网络错误状态，所有请求都会失败，用于测试错误处理。';
        break;
      case MockScenario.slow:
        description = '模拟慢网络状态，请求会有较长延迟，用于测试加载状态。';
        break;
      case MockScenario.unstable:
        description = '模拟不稳定网络，30%的请求会失败，用于测试重试机制。';
        break;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(_getScenarioIcon(scenario)),
            const SizedBox(width: 8),
            Text(_getScenarioLabel(scenario)),
          ],
        ),
        content: Text(description),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}

/// Mock数据测试应用
class MockDataTestApp extends StatelessWidget {
  const MockDataTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ProviderScope(
      // 使用Mock数据
      overrides: getMockProviderOverrides(),
      child: MaterialApp(
        title: 'Mock数据测试',
        theme: ThemeData(
          primarySwatch: Colors.blue,
          useMaterial3: true,
        ),
        home: const MockDataTestPage(),
      ),
    );
  }
}

/// 测试应用入口
void main() {
  runApp(const MockDataTestApp());
}
