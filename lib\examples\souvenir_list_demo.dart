import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../pages/souvenir_list_page.dart';
import '../providers/auth_provider.dart';
import '../mock/mock_providers.dart';
import '../mock/mock_souvenir_service.dart';

/// 奖励列表演示应用
///
/// 展示如何集成奖励列表页面到应用中
/// 包含认证检查和页面导航
class SouvenirListDemoApp extends StatelessWidget {
  const SouvenirListDemoApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ProviderScope(
      // 使用Mock数据进行测试
      overrides: getMockProviderOverrides(),
      child: MaterialApp(
        title: '小鸡旅行 - 奖励列表',
        theme: ThemeData(primarySwatch: Colors.blue, useMaterial3: true),
        home: const DemoHomePage(),
      ),
    );
  }
}

/// 演示主页
class DemoHomePage extends ConsumerStatefulWidget {
  const DemoHomePage({super.key});

  @override
  ConsumerState<DemoHomePage> createState() => _DemoHomePageState();
}

class _DemoHomePageState extends ConsumerState<DemoHomePage> {
  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('小鸡旅行演示'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 认证状态卡片
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '认证状态',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Icon(
                          authState.isAuthenticated ? Icons.check_circle : Icons.cancel,
                          color: authState.isAuthenticated ? Colors.green : Colors.red,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          authState.isAuthenticated ? '已登录' : '未登录',
                          style: TextStyle(
                            color: authState.isAuthenticated ? Colors.green : Colors.red,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    if (authState.user != null) ...[
                      const SizedBox(height: 8),
                      Text('用户: ${authState.user!.nickname}'),
                      Text('UID: ${authState.user!.uid}'),
                    ],
                    if (authState.error != null) ...[
                      const SizedBox(height: 8),
                      Text(
                        '错误: ${authState.error}',
                        style: const TextStyle(color: Colors.red),
                      ),
                    ],
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Mock场景切换
            const Text(
              'Mock数据场景',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),

            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: MockScenario.values.map((scenario) {
                return ChoiceChip(
                  label: Text(_getScenarioLabel(scenario)),
                  selected: MockScenarioManager.currentScenario == scenario,
                  onSelected: (selected) {
                    if (selected) {
                      MockScenarioManager.setScenario(scenario);
                      // 强制刷新页面以应用新场景
                      if (mounted) {
                        setState(() {});
                      }
                    }
                  },
                );
              }).toList(),
            ),

            const SizedBox(height: 16),

            // 功能按钮
            const Text(
              '功能演示',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),

            // 登录按钮
            if (!authState.isAuthenticated) ...[
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: authState.isLoading
                      ? null
                      : () => _showLoginDialog(context, ref),
                  icon: authState.isLoading
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.login),
                  label: const Text('登录'),
                ),
              ),
              const SizedBox(height: 8),
            ],

            // 奖励列表按钮
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(builder: (context) => const SouvenirListPage()),
                  );
                },
                icon: const Icon(Icons.card_giftcard),
                label: const Text('查看奖励列表'),
              ),
            ),

            if (authState.isAuthenticated) ...[
              const SizedBox(height: 8),
              SizedBox(
                width: double.infinity,
                child: OutlinedButton.icon(
                  onPressed: () => ref.read(authProvider.notifier).logout(),
                  icon: const Icon(Icons.logout),
                  label: const Text('登出'),
                ),
              ),
            ],

            const SizedBox(height: 24),

            // 说明文字
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('使用说明', style: TextStyle(fontWeight: FontWeight.bold)),
                  SizedBox(height: 8),
                  Text('1. 点击"登录"按钮进行用户认证（可选）'),
                  Text('2. 点击"查看奖励列表"进入奖励展示页面'),
                  Text('3. 在奖励页面可以切换分类筛选'),
                  Text('4. 支持下拉刷新和上拉加载更多'),
                  Text('5. 需要认证的API会自动添加Token'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 获取场景标签
  String _getScenarioLabel(MockScenario scenario) {
    switch (scenario) {
      case MockScenario.normal:
        return '正常数据';
      case MockScenario.empty:
        return '空数据';
      case MockScenario.error:
        return '错误状态';
      case MockScenario.slow:
        return '慢网络';
      case MockScenario.unstable:
        return '不稳定网络';
    }
  }

  void _showLoginDialog(BuildContext context, WidgetRef ref) {
    final usernameController = TextEditingController(text: 'demo_user');
    final passwordController = TextEditingController(text: '123456');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('登录'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: usernameController,
              decoration: const InputDecoration(
                labelText: '用户名',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: passwordController,
              decoration: const InputDecoration(
                labelText: '密码',
                border: OutlineInputBorder(),
              ),
              obscureText: true,
            ),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Navigator.pop(context), child: const Text('取消')),
          ElevatedButton(
            onPressed: () {
              ref
                  .read(authProvider.notifier)
                  .login(usernameController.text, passwordController.text);
              Navigator.pop(context);
            },
            child: const Text('登录'),
          ),
        ],
      ),
    );
  }
}

/// 演示应用入口
void main() {
  runApp(const SouvenirListDemoApp());
}
