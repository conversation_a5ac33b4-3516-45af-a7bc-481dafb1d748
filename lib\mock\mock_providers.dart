import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/souvenir_provider.dart';
import 'mock_souvenir_service.dart';

/// Mock奖励服务Provider
final mockSouvenirServiceProvider = Provider<MockSouvenirService>((ref) {
  return MockSouvenirService();
});

/// Provider覆盖配置
///
/// 用于在测试时替换真实的Provider
List<Override> getMockProviderOverrides() {
  return [
    // 用Mock服务替换真实服务
    souvenirListProvider.overrideWith((ref, query) {
      final mockService = MockSouvenirService();
      return SouvenirListNotifier(mockService, query);
    }),
  ];
}
