import '../models/souvenir/souvenir_item.dart';
import '../models/api_response.dart';
import 'mock_data.dart';

/// Mock奖励服务
/// 
/// 用于测试的模拟服务，替换真实的API调用
/// 可以模拟各种网络状态和数据情况
class MockSouvenirService {
  // 模拟网络延迟
  static const Duration _networkDelay = Duration(milliseconds: 800);
  
  // 模拟错误概率（0.0 - 1.0）
  static double errorRate = 0.0;
  
  // 模拟数据总数
  static int totalItems = 100;

  /// 查询奖励列表（模拟版本）
  Future<ApiResponse<PaginatedResponse<SouvenirItem>>> getSouvenirs({
    String? category,
    int? page,
    int? limit,
  }) async {
    // 模拟网络延迟
    await Future.delayed(_networkDelay);
    
    // 模拟网络错误
    if (errorRate > 0 && DateTime.now().millisecond / 1000 < errorRate) {
      throw Exception('模拟网络错误：连接超时');
    }
    
    final currentPage = page ?? 1;
    final pageSize = limit ?? 20;
    
    // 特殊情况处理
    if (category == 'empty') {
      return MockDataPresets.emptyResponse;
    }
    
    if (category == 'error') {
      return MockDataPresets.errorResponse;
    }
    
    // 生成模拟数据
    return MockData.generateSouvenirApiResponse(
      page: currentPage,
      limit: pageSize,
      category: category,
    );
  }

  /// 设置错误率（用于测试错误处理）
  static void setErrorRate(double rate) {
    errorRate = rate.clamp(0.0, 1.0);
  }

  /// 设置总数据量
  static void setTotalItems(int total) {
    totalItems = total;
  }

  /// 重置为默认状态
  static void reset() {
    errorRate = 0.0;
    totalItems = 100;
  }
}

/// Mock数据场景
enum MockScenario {
  normal,      // 正常数据
  empty,       // 空数据
  error,       // 错误状态
  slow,        // 慢网络
  unstable,    // 不稳定网络
}

/// Mock场景管理器
class MockScenarioManager {
  static MockScenario _currentScenario = MockScenario.normal;
  
  /// 设置测试场景
  static void setScenario(MockScenario scenario) {
    _currentScenario = scenario;
    
    switch (scenario) {
      case MockScenario.normal:
        MockSouvenirService.setErrorRate(0.0);
        break;
      case MockScenario.empty:
        MockSouvenirService.setTotalItems(0);
        break;
      case MockScenario.error:
        MockSouvenirService.setErrorRate(1.0);
        break;
      case MockScenario.slow:
        // 慢网络在服务中通过延迟模拟
        MockSouvenirService.setErrorRate(0.0);
        break;
      case MockScenario.unstable:
        MockSouvenirService.setErrorRate(0.3); // 30%错误率
        break;
    }
  }
  
  /// 获取当前场景
  static MockScenario get currentScenario => _currentScenario;
  
  /// 重置到正常场景
  static void reset() {
    setScenario(MockScenario.normal);
    MockSouvenirService.reset();
  }
}
