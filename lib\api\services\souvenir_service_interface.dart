import '../../models/souvenir/souvenir_item.dart';
import '../../models/api_response.dart';

/// 奖励服务接口
/// 
/// 定义奖励服务的通用接口，可以被真实服务和Mock服务实现
abstract class ISouvenirService {
  /// 查询奖励列表
  /// 
  /// [category] 分组（可选）
  /// [page] 页码（可选）
  /// [limit] 每页个数（可选）
  /// 返回分页的奖励列表
  Future<ApiResponse<PaginatedResponse<SouvenirItem>>> getSouvenirs({
    String? category,
    int? page,
    int? limit,
  });
}
