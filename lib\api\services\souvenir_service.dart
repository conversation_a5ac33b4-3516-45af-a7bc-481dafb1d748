import 'package:dio/dio.dart';
import '../network/http_client.dart';
import '../../models/souvenir/souvenir_item.dart';
import '../../models/api_response.dart';
import 'souvenir_service_interface.dart';

/// 奖励服务类
///
/// 处理奖励相关的API请求
class SouvenirService implements ISouvenirService {
  final HttpClient _httpClient;

  SouvenirService(this._httpClient);

  /// 查询奖励列表
  ///
  /// [category] 分组（可选）
  /// [page] 页码（可选）
  /// [limit] 每页个数（可选）
  /// 返回分页的奖励列表
  @override
  Future<ApiResponse<PaginatedResponse<SouvenirItem>>> getSouvenirs({
    String? category,
    int? page,
    int? limit,
  }) async {
    try {
      final queryParameters = <String, dynamic>{};
      if (category != null) queryParameters['category'] = category;
      if (page != null) queryParameters['page'] = page;
      if (limit != null) queryParameters['limit'] = limit;

      final response = await _httpClient.get(
        '/souvenirs',
        queryParameters: queryParameters.isNotEmpty ? queryParameters : null,
      );

      return ApiResponse.fromJson(
        response.data as Map<String, dynamic>,
        (data) => PaginatedResponse.fromJson(
          data as Map<String, dynamic>,
          (item) => SouvenirItem.fromJson(item),
        ),
      );
    } on DioException catch (e) {
      throw _handleDioException(e);
    }
  }

  /// 处理Dio异常
  Exception _handleDioException(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return Exception('网络连接超时，请检查网络设置');
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        final message = e.message ?? '请求失败';
        if (statusCode == 401) {
          return Exception('认证失败，请重新登录');
        } else if (statusCode == 403) {
          return Exception('权限不足');
        } else if (statusCode == 404) {
          return Exception('请求的资源不存在');
        } else if (statusCode == 500) {
          return Exception('服务器内部错误');
        }
        return Exception('请求失败: $message');
      case DioExceptionType.cancel:
        return Exception('请求已取消');
      case DioExceptionType.connectionError:
        return Exception('网络连接错误，请检查网络设置');
      default:
        return Exception('未知错误: ${e.message}');
    }
  }
}
