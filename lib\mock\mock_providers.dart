import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/souvenir_provider.dart';
import 'mock_souvenir_service.dart';

/// Mock奖励服务Provider
final mockSouvenirServiceProvider = Provider<MockSouvenirService>((ref) {
  return MockSouvenirService();
});

/// Provider覆盖配置
///
/// 用于在测试时替换真实的Provider
List<Override> getMockProviderOverrides() {
  return [
    // 用Mock服务替换真实服务
    souvenirListProvider.overrideWith((ref, query) {
      final mockService = MockSouvenirService();
      // 创建一个适配器来桥接 MockSouvenirService 和 SouvenirListNotifier
      return _MockSouvenirListNotifierAdapter(mockService, query);
    }),
  ];
}

/// 适配器类，用于桥接 MockSouvenirService 和 SouvenirListNotifier
class _MockSouvenirListNotifierAdapter extends SouvenirListNotifier {
  final MockSouvenirService _mockService;
  final SouvenirQuery _initialQuery;

  _MockSouvenirListNotifierAdapter(this._mockService, this._initialQuery)
    : super(_mockService as dynamic, _initialQuery);

  @override
  Future<void> loadSouvenirs({bool refresh = false}) async {
    if (state.isLoading) return;

    final query = refresh
        ? _initialQuery
        : _initialQuery.copyWith(page: state.pagination?.currentPage ?? 1);

    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _mockService.getSouvenirs(
        category: query.category,
        page: query.page,
        limit: query.limit,
      );

      if (response.isSuccess && response.data != null) {
        final data = response.data!;
        final newItems = refresh ? data.items : [...state.items, ...data.items];

        state = state.copyWith(
          items: newItems,
          pagination: data.pagination,
          isLoading: false,
          hasMore: data.pagination.hasNextPage,
        );
      } else {
        state = state.copyWith(isLoading: false, error: response.message);
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  @override
  Future<void> loadMore() async {
    if (!state.hasMore || state.isLoading) return;

    final nextPage = (state.pagination?.currentPage ?? 0) + 1;
    final query = _initialQuery.copyWith(page: nextPage);

    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _mockService.getSouvenirs(
        category: query.category,
        page: query.page,
        limit: query.limit,
      );

      if (response.isSuccess && response.data != null) {
        final data = response.data!;
        final newItems = [...state.items, ...data.items];

        state = state.copyWith(
          items: newItems,
          pagination: data.pagination,
          isLoading: false,
          hasMore: data.pagination.hasNextPage,
        );
      } else {
        state = state.copyWith(isLoading: false, error: response.message);
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }
}
